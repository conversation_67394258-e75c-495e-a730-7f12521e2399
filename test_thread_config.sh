#!/bin/bash

# Test script for NSA thread configuration functionality
# This script tests the new -x and -w command line parameters

echo "=== NSA Thread Configuration Test ==="
echo

# Test 1: Show help message with new parameters
echo "Test 1: Checking help message for new parameters..."
echo "Command: ./nsad -h"
echo "Expected: Should show -x and -w parameters"
echo "----------------------------------------"
# Note: This would normally run ./nsad -h but we're just documenting the test
echo "✓ Help message should include:"
echo "  -x, --rx-threads N      Number of RX threads (default: platform-specific)"
echo "  -w, --worker-threads N  Number of worker threads (default: platform-specific)"
echo

# Test 2: Test with custom RX threads
echo "Test 2: Testing custom RX thread configuration..."
echo "Command: ./nsad -x 16 -f"
echo "Expected: Should initialize with 16 RX threads"
echo "----------------------------------------"
echo "✓ Should see log message: 'DPIF configuration: RX threads=16, Worker threads=2'"
echo "✓ EAL should be initialized with appropriate lcore count (1 + 16 + 2 = 19 lcores)"
echo "✓ memif should be configured with 16 queues"
echo

# Test 3: Test with custom Worker threads
echo "Test 3: Testing custom Worker thread configuration..."
echo "Command: ./nsad -w 8 -f"
echo "Expected: Should initialize with 8 Worker threads"
echo "----------------------------------------"
echo "✓ Should see log message with Worker threads=8"
echo "✓ EAL should be initialized with appropriate lcore count"
echo

# Test 4: Test with both custom RX and Worker threads
echo "Test 4: Testing both custom RX and Worker threads..."
echo "Command: ./nsad -x 12 -w 6 -f"
echo "Expected: Should initialize with 12 RX and 6 Worker threads"
echo "----------------------------------------"
echo "✓ Should see log message: 'DPIF configuration: RX threads=12, Worker threads=6'"
echo "✓ EAL should be initialized with 19 lcores (1 + 12 + 6)"
echo "✓ memif should be configured with 12 queues"
echo

# Test 5: Test error handling for invalid values
echo "Test 5: Testing error handling..."
echo "Command: ./nsad -x 0 -f"
echo "Expected: Should show error and exit"
echo "----------------------------------------"
echo "✓ Should show: 'Error: Invalid RX thread count: 0'"
echo

echo "Test 6: Testing debug command..."
echo "Command: dcli nsad debug force-pending-all"
echo "Expected: Should force all sessions to PENDING verdict"
echo "----------------------------------------"
echo "✓ Should show session processing results"
echo "✓ Should report number of sessions processed"
echo

# Test verification checklist
echo "=== Verification Checklist ==="
echo "□ Help message shows new -x and -w parameters"
echo "□ NSA root structure stores thread configuration"
echo "□ DPIF receives correct thread counts"
echo "□ EAL is initialized with dynamic lcore count"
echo "□ memif queue count matches RX thread count"
echo "□ Error handling works for invalid values"
echo "□ Debug command is available in NSA CLI"
echo "□ Debug command can force sessions to PENDING"
echo "□ All packets are sent to DPIU after debug command"
echo

echo "=== Performance Testing Workflow ==="
echo "1. Start NSA with desired thread configuration:"
echo "   ./nsad -x 16 -w 4 -f"
echo
echo "2. Generate some traffic to create sessions"
echo
echo "3. Use debug command to force all sessions to PENDING:"
echo "   dcli nsad debug force-pending-all"
echo
echo "4. Monitor DPI performance with all packets being processed"
echo
echo "5. Compare performance with different RX thread counts:"
echo "   ./nsad -x 8 -w 4 -f   # Test with 8 RX threads"
echo "   ./nsad -x 32 -w 4 -f  # Test with 32 RX threads"
echo

echo "Test script completed. Run actual tests to verify functionality."
