#include <arpa/inet.h>  // For inet_ntop, inet_pton
#include <ctype.h>      // For isspace, isdigit
#include <errno.h>
#include <netinet/in.h>  // For AF_INET, AF_INET6
#include <poll.h>
#include <pthread.h>
#include <rte_cycles.h>
#include <rte_hash.h>
#include <rte_malloc.h>
#include <rte_mempool.h>
#include <rte_ring.h>
#include <rte_string_fns.h>
#include <rte_timer.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>

#include "dpif.h"
#include "dpif_init.h"
#include "dpif_private.h"  // dpif_rx_context_t now has IPv6 fields for injection
#include "dpif_stats.h"

/* Use DPIF logging system instead of RTE_LOG */
#define DPIF_CLI_LOG_ERROR(fmt, ...) DPIF_LOG_ERROR("[DPIF_CLI] " fmt, ##__VA_ARGS__)
#define DPIF_CLI_LOG_WARNING(fmt, ...) DPIF_LOG_WARNING("[DPIF_CLI] " fmt, ##__VA_ARGS__)
#define DPIF_CLI_LOG_INFO(fmt, ...) DPIF_LOG_INFO("[DPIF_CLI] " fmt, ##__VA_ARGS__)
#define DPIF_CLI_LOG_DEBUG(fmt, ...) DPIF_LOG_DEBUG("[DPIF_CLI] " fmt, ##__VA_ARGS__)

#define CLI_SOCKET_PATH "/tmp/dpif_cli.sock"
#define CLI_CMD_BUFFER_SIZE 1024                // For incoming commands
#define CLI_RESPONSE_BUFFER_SIZE (1024 * 1024)  // For outgoing responses, make it generous
#define MAX_CLI_ARGS 16
#define DEFAULT_LS_COUNT 20
#define MAX_SESSIONS_DISPLAY_AT_ONCE 512  // Sensible internal limit for a single ls call if needed, can be adjusted

extern dpif_global_context_t *g_dpif_ctx;  // Defined in dpif.c, externed elsewhere

static volatile int cli_server_quit_flag = 0;
static pthread_t cli_server_tid = (pthread_t) 0;

// --- Statistics Snapshot Structure (for calculating rates in dashboard) ---
typedef struct {
    uint64_t tsc;  // Timestamp for the snapshot
    dpif_global_stats_t global;
    dpif_core_stats_t core[RTE_MAX_LCORE];  // Store previous stats per core
} monitor_stats_snapshot_t;

static monitor_stats_snapshot_t dashboard_prev_stats = {0};
static int dashboard_prev_stats_initialized = 0;
static pthread_mutex_t dashboard_stats_mutex = PTHREAD_MUTEX_INITIALIZER;  // Protect prev_stats

// --- Command Handlers Forward Declarations ---
/** @brief Handles the 'help' command. */
static void cli_cmd_help(int client_sock, int argc, char *argv[], char *response_buf);
/** @brief Handles the 'show_global_stats' (and 'sgs') command. */
static void cli_cmd_show_global_stats(int client_sock, int argc, char *argv[], char *response_buf);
/** @brief Handles the 'show_core_stats' (and 'scs') command. */
static void cli_cmd_show_core_stats(int client_sock, int argc, char *argv[], char *response_buf);
/** @brief Handles the 'show_core_totals' (and 'sct') command. */
static void cli_cmd_show_core_totals(int client_sock, int argc, char *argv[], char *response_buf);
/** @brief Handles the 'list_sessions' (and 'ls') command. */
static void cli_cmd_list_sessions(int client_sock, int argc, char *argv[], char *response_buf);

/** @brief Handles the 'dashboard' (and 'd') command. */
static void cli_cmd_show_dashboard(int client_sock, int argc, char *argv[], char *response_buf);
/** @brief Handles the 'inject' command. */
static void cli_cmd_inject_packets(int client_sock, int argc, char *argv[], char *response_buf);
/** @brief Handles the 'set_mode' command. */
static void cli_cmd_set_mode(int client_sock, int argc, char *argv[], char *response_buf);
/** @brief Handles the 'show_config' (and 'scfg') command. */
static void cli_cmd_show_config(int client_sock, int argc, char *argv[], char *response_buf);
/** @brief Handles the 'set_verdict' command. (NEW) */
static void cli_cmd_set_verdict(int client_sock, int argc, char *argv[], char *response_buf);

static void cli_cmd_pcap_replay(int, int, char *[], char *);
static void cli_cmd_force_all_pending(int, int, char *[], char *);
// --- Command Table ---

/**
 * @brief Type definition for a CLI command handler function.
 */
typedef void (*cli_command_handler_t)(int client_sock, int argc, char *argv[], char *response_buf);

/**
 * @brief Structure representing an entry in the CLI command table.
 */
typedef struct {
    const char *name;
    cli_command_handler_t handler;
    const char *help_summary;  // Short summary for general help
    const char *help_detail;   // Detailed usage
    int min_args;
} cli_command_entry_t;

static const cli_command_entry_t cli_command_table[] = {
    {"help", cli_cmd_help, "Show help message.", "Usage: help [command_name]", 1},
    {"show_global_stats", cli_cmd_show_global_stats, "Show global DPIF statistics.", "Usage: show_global_stats", 1},
    {"sgs", cli_cmd_show_global_stats, "Alias for show_global_stats.", "Usage: sgs", 1},
    {"show_core_stats",
     cli_cmd_show_core_stats,
     "Show lcore stats (current values/rates).",
     "Usage: show_core_stats <lcore_id|all>",
     2},
    {"scs", cli_cmd_show_core_stats, "Alias for show_core_stats.", "Usage: scs <lcore_id|all>", 2},
    {"show_core_totals",
     cli_cmd_show_core_totals,
     "Show lcore total accumulated statistics.",
     "Usage: show_core_totals <lcore_id|all>",
     2},
    {"sct", cli_cmd_show_core_totals, "Alias for show_core_totals.", "Usage: sct <lcore_id|all>", 2},
    {"list_sessions",
     cli_cmd_list_sessions,
     "List active sessions.",
     "Usage: list_sessions [ <max_count (default " RTE_STR(DEFAULT_LS_COUNT) ", max " RTE_STR(
         MAX_SESSIONS_DISPLAY_AT_ONCE) ")> ]",
     1},
    {"ls",
     cli_cmd_list_sessions,
     "Alias for list_sessions.",
     "Usage: ls [ <max_count (default " RTE_STR(DEFAULT_LS_COUNT) ", max " RTE_STR(MAX_SESSIONS_DISPLAY_AT_ONCE) ")> ]",
     1},
    {"dashboard", cli_cmd_show_dashboard, "Show a one-time snapshot of the system dashboard.", "Usage: dashboard", 1},
    {"d", cli_cmd_show_dashboard, "Alias for dashboard.", "Usage: d", 1},
    {"inject",
     cli_cmd_inject_packets,
     "Inject simulated packets to an RX lcore.",
     "Usage: inject <rx_lcore_id> <count> <ipv4|ipv6> \\\n"  // Added address family
     "       [src_ip sport dst_ip dport proto payload_len (all 6 must be provided if any custom)]",
     4},  // Min args now 4
    {"set_mode", cli_cmd_set_mode, "Set DPIF operating mode.", "Usage: set_mode <normal|bypass_verdict>", 2},
    {"show_config", cli_cmd_show_config, "Show current DPIF system configuration.", "Usage: show_config", 1},
    {"scfg", cli_cmd_show_config, "Alias for show_config.", "Usage: scfg", 1},
    {"set_verdict",
     cli_cmd_set_verdict,
     "Manually set a verdict for a specific session.",
     "Usage: set_verdict <session_id> <accept|drop>",
     3},
    {"pcap-replay", cli_cmd_pcap_replay, "Start or stop a PCAP file replay.", "Usage: pcap-replay <start|stop> ...", 2},
    {"force-all-pending",
     cli_cmd_force_all_pending,
     "Force all active sessions to PENDING verdict for DPI performance testing.",
     "Usage: force-all-pending",
     1},
    {NULL, NULL, NULL, NULL, 0}};

/**
 * @brief Safely appends formatted text to the response buffer.
 *
 * @param response_buf The buffer to append to.
 * @param buf_size The total size of the response buffer.
 * @param format The format string.
 * @param ... Variable arguments for the format string.
 */
static void append_to_response(char *response_buf, size_t buf_size, const char *format, ...) {
    va_list args;
    size_t current_len = strlen(response_buf);
    if (current_len < buf_size - 1) {  // -1 for null terminator
        va_start(args, format);
        vsnprintf(response_buf + current_len, buf_size - current_len, format, args);
        va_end(args);
    }
}

/**
 * @brief Parses an IP address string (IPv4 or IPv6).
 *
 * @param ip_str The IP address string to parse.
 * @param af The address family (AF_INET or AF_INET6).
 * @param ip_addr_out Pointer to the output buffer.
 *                    For AF_INET, this should be a uint32_t* (IP stored in host byte order).
 *                    For AF_INET6, this should be a uint8_t[16] (IP stored in network byte order).
 * @return 0 on success, -1 on failure.
 */
static int parse_ip_addr_cli(const char *ip_str, uint8_t af, void *ip_addr_out) {
    if (af == AF_INET) {
        struct in_addr addr_in4;
        if (inet_pton(AF_INET, ip_str, &addr_in4) == 1) {
            // For IPv4, we store it as uint32_t in host byte order in inject_ip_u
            *((uint32_t *) ip_addr_out) = rte_be_to_cpu_32(addr_in4.s_addr);
            return 0;
        }
    } else if (af == AF_INET6) {
        struct in6_addr addr_in6;
        if (inet_pton(AF_INET6, ip_str, &addr_in6) == 1) {
            // For IPv6, we store raw 16 bytes (which are NBO from inet_pton)
            memcpy(ip_addr_out, addr_in6.s6_addr, 16);
            return 0;
        }
    }
    return -1;  // Invalid address family or parse error
}

/**
 * @brief Handles the 'inject' CLI command to simulate packet injection.
 */
static void cli_cmd_inject_packets(int client_sock __rte_unused, int argc, char *argv[], char *response_buf) {
    if (!g_dpif_ctx || !g_dpif_ctx->rx_contexts) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: DPIF or RX contexts not initialized.\n");
        return;
    }

#if !PCAP_PACKETS
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "Error: Packet injection is only available when compiled with SIMULATE_PACKETS=1.\n");
    return;
#endif

    char *endptr;
    long lcore_id_long = strtol(argv[1], &endptr, 10);
    if (*endptr != '\0' || lcore_id_long < 0 || lcore_id_long >= RTE_MAX_LCORE) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid rx_lcore_id '%s'.\n", argv[1]);
        return;
    }
    uint32_t target_lcore_id = (uint32_t) lcore_id_long;

    int is_valid_rx_core = 0;
    for (uint32_t i = 0; i < g_dpif_ctx->num_rx_cores; ++i) {
        if (g_dpif_ctx->rx_lcore_map[i] == target_lcore_id) {
            if (rte_lcore_is_enabled(target_lcore_id) &&
                g_dpif_ctx->rx_contexts[target_lcore_id].lcore_id == target_lcore_id) {
                is_valid_rx_core = 1;
            }
            break;
        }
    }
    if (!is_valid_rx_core) {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "Error: Lcore %u is not an enabled or active DPIF RX core.\n",
                           target_lcore_id);
        return;
    }

    long count_long = strtol(argv[2], &endptr, 10);
    if (*endptr != '\0' || count_long <= 0) {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid count '%s'. Must be a positive number.\n", argv[2]);
        return;
    }
    int32_t count_to_add = (int32_t) count_long;

    // New argument for address family
    uint8_t af_inject;
    if (strcmp(argv[3], "ipv4") == 0) {
        af_inject = AF_INET;
    } else if (strcmp(argv[3], "ipv6") == 0) {
        af_inject = AF_INET6;
    } else {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "Error: Invalid address family '%s'. Use 'ipv4' or 'ipv6'.\n",
                           argv[3]);
        return;
    }

    dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[target_lcore_id];
    rx_ctx->inject_address_family = af_inject;  // Store the address family

    if (argc == 4) {                    // Only lcore, count, and address family provided
        rx_ctx->inject_params_set = 0;  // Use default/random params within construct_dummy_packet
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "OK: Will inject %d %s packets to RX lcore %u using default parameters.\n",
                           count_to_add,
                           (af_inject == AF_INET6 ? "IPv6" : "IPv4"),
                           target_lcore_id);
    } else if (argc == 10) {
        uint16_t s_port_val, d_port_val, p_len_val;
        long temp_long_val;
        uint8_t proto_val_val;

        if (af_inject == AF_INET) {
            if (parse_ip_addr_cli(argv[4], AF_INET, &rx_ctx->inject_src_ip_u.ipv4_src_ip) != 0) {
                append_to_response(
                    response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid IPv4 src_ip '%s'.\n", argv[4]);
                return;
            }
            if (parse_ip_addr_cli(argv[6], AF_INET, &rx_ctx->inject_dst_ip_u.ipv4_dst_ip) != 0) {
                append_to_response(
                    response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid IPv4 dst_ip '%s'.\n", argv[6]);
                return;
            }
        } else {  // AF_INET6
            if (parse_ip_addr_cli(argv[4], AF_INET6, rx_ctx->inject_src_ip_u.ipv6_src_ip) != 0) {
                append_to_response(
                    response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid IPv6 src_ip '%s'.\n", argv[4]);
                return;
            }
            if (parse_ip_addr_cli(argv[6], AF_INET6, rx_ctx->inject_dst_ip_u.ipv6_dst_ip) != 0) {
                append_to_response(
                    response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid IPv6 dst_ip '%s'.\n", argv[6]);
                return;
            }
        }

        temp_long_val = strtol(argv[5], &endptr, 10);  // sport
        if (*endptr != '\0' || temp_long_val < 0 || temp_long_val > 65535) {
            append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid sport '%s'.\n", argv[5]);
            return;
        }
        s_port_val = (uint16_t) temp_long_val;

        temp_long_val = strtol(argv[7], &endptr, 10);  // dport
        if (*endptr != '\0' || temp_long_val < 0 || temp_long_val > 65535) {
            append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid dport '%s'.\n", argv[7]);
            return;
        }
        d_port_val = (uint16_t) temp_long_val;

        if (strcmp(argv[8], "tcp") == 0)
            proto_val_val = IPPROTO_TCP;
        else if (strcmp(argv[8], "udp") == 0)
            proto_val_val = IPPROTO_UDP;
        else {
            temp_long_val = strtol(argv[8], &endptr, 10);
            if (*endptr != '\0' || temp_long_val < 0 || temp_long_val > 255) {
                append_to_response(response_buf,
                                   CLI_RESPONSE_BUFFER_SIZE,
                                   "Error: Invalid proto '%s'. Use 'tcp', 'udp', or number.\n",
                                   argv[8]);
                return;
            }
            proto_val_val = (uint8_t) temp_long_val;
        }

        temp_long_val = strtol(argv[9], &endptr, 10);  // payload_len
        if (*endptr != '\0' || temp_long_val < 0 ||
            temp_long_val > 1400) {  // Adjusted max payload for typical Ethernet MTU and headers
            append_to_response(
                response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid payload_len '%s' (0-1400).\n", argv[9]);
            return;
        }
        p_len_val = (uint16_t) temp_long_val;

        // IPs already stored in the union by parse_ip_addr_cli
        rx_ctx->inject_src_port = s_port_val;
        rx_ctx->inject_dst_port = d_port_val;
        rx_ctx->inject_proto = proto_val_val;
        rx_ctx->inject_payload_len = p_len_val;
        rx_ctx->inject_params_set = 1;

        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "OK: Will inject %d %s packets to RX lcore %u with custom parameters.\n",
                           count_to_add,
                           (af_inject == AF_INET6 ? "IPv6" : "IPv4"),
                           target_lcore_id);
    } else {
        const char *inject_help_detail = "Usage: inject <rx_lcore_id> <count> <ipv4|ipv6> [src_ip sport dst_ip dport "
                                         "proto payload_len]";
        for (int i = 0; cli_command_table[i].name != NULL; ++i) {
            if (strcmp(cli_command_table[i].name, "inject") == 0) {
                inject_help_detail = cli_command_table[i].help_detail;
                break;
            }
        }
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "Error: Incorrect number of arguments for custom parameters.\n%s\n",
                           inject_help_detail);
        return;
    }

    int32_t old_val = rte_atomic32_add_return(&rx_ctx->inject_packet_count, count_to_add);
    DPIF_CLI_LOG_DEBUG(
        "CLI added %d packets to RX lcore %u. New total pending: %d", count_to_add, target_lcore_id, old_val);
}

static void handle_pcap_replay_start(int client_sock, int argc, char *argv[], char *response_buf) {
    if (argc < 4) {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "Usage: pcap-replay start <lcore_id> <filepath> [count]\n");
        return;
    }

    char *endptr;
    long lcore_id_long = strtol(argv[2], &endptr, 10);
    if (*endptr != '\0' || lcore_id_long < 0 || lcore_id_long >= RTE_MAX_LCORE ||
        !rte_lcore_is_enabled(lcore_id_long)) {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid or disabled lcore_id '%s'.\n", argv[2]);
        return;
    }
    uint32_t target_lcore_id = (uint32_t) lcore_id_long;
    dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[target_lcore_id];

    char real_path[PATH_MAX];
    if (realpath(argv[3], real_path) == NULL) {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid PCAP file path or file not found: %s\n", argv[3]);
        return;
    }

    long count = -1;
    if (argc > 4) {
        count = strtol(argv[4], &endptr, 10);
        if (*endptr != '\0' || count <= 0) {
            append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid count '%s'.\n", argv[4]);
            return;
        }
    }
    if (count == -1)
        count = INT_MAX;

    strncpy(rx_ctx->pcap_filepath, real_path, sizeof(rx_ctx->pcap_filepath) - 1);
    rx_ctx->pcap_filepath[sizeof(rx_ctx->pcap_filepath) - 1] = '\0';
    rte_atomic32_set(&rx_ctx->pcap_packets_to_inject, count);
    rx_ctx->pcap_replay_request = 1;

    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "OK: Started PCAP replay of '%s' on lcore %u.\n",
                       rx_ctx->pcap_filepath,
                       target_lcore_id);
}

static void handle_pcap_replay_stop(int client_sock, int argc, char *argv[], char *response_buf) {
    if (argc < 3) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Usage: pcap-replay stop <lcore_id>\n");
        return;
    }

    char *endptr;
    long lcore_id_long = strtol(argv[2], &endptr, 10);
    if (*endptr != '\0' || lcore_id_long < 0 || lcore_id_long >= RTE_MAX_LCORE ||
        !rte_lcore_is_enabled(lcore_id_long)) {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid or disabled lcore_id '%s'.\n", argv[2]);
        return;
    }
    uint32_t target_lcore_id = (uint32_t) lcore_id_long;
    dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[target_lcore_id];

    if (!rx_ctx->pcap_replay_request) {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "Info: No PCAP replay is currently active on lcore %u.\n",
                           target_lcore_id);
        return;
    }

    rte_atomic32_set(&rx_ctx->pcap_packets_to_inject, 0);
    rx_ctx->pcap_replay_request = 0;

    append_to_response(
        response_buf, CLI_RESPONSE_BUFFER_SIZE, "OK: Sent stop signal to PCAP replay on lcore %u.\n", target_lcore_id);
}

static void cli_cmd_pcap_replay(int client_sock, int argc, char *argv[], char *response_buf) {
#if !PCAP_PACKETS
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "Error: PCAP replay is only available when compiled with SIMULATE_PACKETS=1.\n");
    return;
#endif

    if (argc < 2) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Usage: pcap-replay <start|stop> ...\n");
        return;
    }

    if (strcmp(argv[1], "start") == 0) {
        handle_pcap_replay_start(client_sock, argc, argv, response_buf);
    } else if (strcmp(argv[1], "stop") == 0) {
        handle_pcap_replay_stop(client_sock, argc, argv, response_buf);
    } else {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "Error: Unknown subcommand '%s'. Use 'start' or 'stop'.\n",
                           argv[1]);
    }
}

/**
 * @brief Handles the 'dashboard' CLI command to display a system snapshot.
 */
static void cli_cmd_show_dashboard(int client_sock __rte_unused,
                                   int argc __rte_unused,
                                   char *argv[] __rte_unused,
                                   char *response_buf) {
    if (!g_dpif_ctx) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: DPIF context not initialized.\n");
        return;
    }

    uint32_t lcore_id;
    uint64_t hz = rte_get_timer_hz();

    dpif_global_stats_t current_global_stats;
    dpif_core_stats_t current_core_stats[RTE_MAX_LCORE];
    memset(current_core_stats, 0, sizeof(current_core_stats));

    if (dpif_get_global_stats(&current_global_stats) < 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error getting global stats.\n");
        return;
    }
    RTE_LCORE_FOREACH_WORKER(lcore_id) {
        if (rte_lcore_is_enabled(lcore_id)) {
            dpif_get_core_stats(lcore_id, &current_core_stats[lcore_id]);
        }
    }
    uint64_t current_tsc_dashboard = rte_rdtsc();

    pthread_mutex_lock(&dashboard_stats_mutex);

    double total_interval_sec_dashboard = 1.0;
    if (dashboard_prev_stats_initialized) {
        if (current_tsc_dashboard > dashboard_prev_stats.tsc) {
            total_interval_sec_dashboard = (double) (current_tsc_dashboard - dashboard_prev_stats.tsc) / hz;
        }
        if (total_interval_sec_dashboard <= 1e-9)
            total_interval_sec_dashboard = 1e-9;
    }

    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "--- System Dashboard (Snapshot) ---\n");
    if (dashboard_prev_stats_initialized) {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "(Rates calculated over approx %.2fs since last 'dashboard' command or app start)\n",
                           total_interval_sec_dashboard);
    } else {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "(First run, rates will appear on subsequent calls if interval > 0)\n");
    }
    //append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Timestamp: %" PRIu64 "\n", current_tsc_dashboard);
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "-----------------------------------------------------------------------------------------------"
                       "-----------------------------------------------------------------------------------------------"
                       "--------------------------\n");
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "| Core | Type   | RX PPS  | Drop PPS| Offload TPS| Comp MPS| Task "
                       "TPS| Update SPS| Sessions | Comp Ring | Task Ring | Decap(cyc) | Lookup(cyc) | Encap(cyc) | "
                       "Analyze(cyc) | Offload(cyc) | TaskProc(cyc) | Notify(cyc) |\n");
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "|------|--------|---------|---------|------------|---------|---------|"
                       "-----------|----------|-----------|-----------|------------|-------------|------------|-------"
                       "-------|--------------|---------------|-------------|\n");

    RTE_LCORE_FOREACH_WORKER(lcore_id) {
        if (!rte_lcore_is_enabled(lcore_id) &&
            lcore_id != rte_get_main_lcore())  // Also show main lcore if it's the only one
            continue;

        dpif_core_stats_t *cur = &current_core_stats[lcore_id];
        dpif_core_stats_t *prev = &dashboard_prev_stats.core[lcore_id];

        if (strcmp(cur->type, "Idle") == 0 && cur->rx_pkts == 0 && cur->processed_tasks == 0) {
            int is_dpif_core = 0;
            if (g_dpif_ctx->rx_contexts && g_dpif_ctx->rx_contexts[lcore_id].lcore_id == lcore_id &&
                g_dpif_ctx->rx_contexts[lcore_id].session_table)
                is_dpif_core = 1;
            if (g_dpif_ctx->worker_contexts && g_dpif_ctx->worker_contexts[lcore_id].lcore_id == lcore_id &&
                g_dpif_ctx->worker_contexts[lcore_id].task_rings)
                is_dpif_core = 1;
            if (!is_dpif_core)
                continue;
        }

        if (strcmp(cur->type, "RX") == 0) {
            double rx_pps = 0, drop_pps = 0, off_tps = 0, comp_mps = 0, upd_sps_rx = 0;
            double avg_lookup = 0, avg_decap = 0, avg_bench = 0, avg_analyze = 0, avg_offload = 0;
            uint64_t diff_rx = 0, diff_off = 0, diff_analyzed = 0;

            if (dashboard_prev_stats_initialized) {
                rx_pps = (cur->rx_pkts >= prev->rx_pkts)
                    ? (double) (cur->rx_pkts - prev->rx_pkts) / total_interval_sec_dashboard
                    : 0;
                drop_pps = (cur->dropped_pkts >= prev->dropped_pkts)
                    ? (double) (cur->dropped_pkts - prev->dropped_pkts) / total_interval_sec_dashboard
                    : 0;
                off_tps = (cur->tasks_offloaded >= prev->tasks_offloaded)
                    ? (double) (cur->tasks_offloaded - prev->tasks_offloaded) / total_interval_sec_dashboard
                    : 0;
                comp_mps = (cur->completion_msgs_processed >= prev->completion_msgs_processed)
                    ? (double) (cur->completion_msgs_processed - prev->completion_msgs_processed) /
                        total_interval_sec_dashboard
                    : 0;
                upd_sps_rx = (cur->updated_sessions_by_timer >= prev->updated_sessions_by_timer)
                    ? (double) (cur->updated_sessions_by_timer - prev->updated_sessions_by_timer) /
                        total_interval_sec_dashboard
                    : 0;
                diff_analyzed =
                    (cur->analyzed_pkts >= prev->analyzed_pkts) ? cur->analyzed_pkts - prev->analyzed_pkts : 0;
                diff_rx = (cur->rx_pkts >= prev->rx_pkts) ? cur->rx_pkts - prev->rx_pkts : 0;
                diff_off =
                    (cur->tasks_offloaded >= prev->tasks_offloaded) ? cur->tasks_offloaded - prev->tasks_offloaded : 0;
                avg_lookup = (diff_rx > 0 && cur->session_lookup_cycles >= prev->session_lookup_cycles)
                    ? (double) (cur->session_lookup_cycles - prev->session_lookup_cycles) / diff_rx
                    : 0;
                avg_decap = (diff_rx > 0 && cur->decap_cycles >= prev->decap_cycles)
                    ? (double) (cur->decap_cycles - prev->decap_cycles) / diff_rx
                    : 0;
                avg_bench = (diff_rx > 0 && cur->benchmark_process_cycles >= prev->benchmark_process_cycles)
                    ? (double) (cur->benchmark_process_cycles - prev->benchmark_process_cycles) / diff_rx
                    : 0;
                avg_analyze = (diff_analyzed > 0 && cur->analyze_cycles >= prev->analyze_cycles)
                    ? (double) (cur->analyze_cycles - prev->analyze_cycles) / diff_analyzed
                    : 0;
                avg_offload = (diff_off > 0 && cur->offload_cycles >= prev->offload_cycles)
                    ? (double) (cur->offload_cycles - prev->offload_cycles) / diff_off
                    : 0;
            }
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "| %4u | %-6s | %7.0f | %7.0f | %10.0f | %7.0f | %7s | %9.0f | %8" PRIu64 " | %9u | %9s "
                               "| %10.1f | %11.1f | %10.1f | %12.1f | %12.1f | %13s | %11s |\n",
                               lcore_id,
                               cur->type,
                               rx_pps,
                               drop_pps,
                               off_tps,
                               comp_mps,
                               "-",
                               upd_sps_rx,
                               cur->sessions,
                               cur->completion_ring_count,
                               "-",
                               avg_decap,
                               avg_lookup,
                               avg_bench,
                               avg_analyze,
                               avg_offload,
                               "-",
                               "-");
        } else if (strcmp(cur->type, "Worker") == 0) {
            double proc_tps = 0;
            double avg_task_proc = 0, avg_notify = 0;
            uint64_t diff_proc = 0;

            if (dashboard_prev_stats_initialized) {
                proc_tps = (cur->processed_tasks >= prev->processed_tasks)
                    ? (double) (cur->processed_tasks - prev->processed_tasks) / total_interval_sec_dashboard
                    : 0;
                diff_proc =
                    (cur->processed_tasks >= prev->processed_tasks) ? cur->processed_tasks - prev->processed_tasks : 0;
                avg_task_proc = (diff_proc > 0 && cur->task_processing_cycles >= prev->task_processing_cycles)
                    ? (double) (cur->task_processing_cycles - prev->task_processing_cycles) / diff_proc
                    : 0;
                avg_notify = (diff_proc > 0 && cur->notify_cycles >= prev->notify_cycles)
                    ? (double) (cur->notify_cycles - prev->notify_cycles) / diff_proc
                    : 0;
            }
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "| %4u | %-6s | %7s | %7s | %10s | %7s | %7.0f | %9s | %8s | %9s | %9u | %10s | %11s "
                               "| %10s | %12s | %12s | %13.1f | %11.1f |\n",
                               lcore_id,
                               cur->type,
                               "-",
                               "-",
                               "-",
                               "-",
                               proc_tps,
                               "-",
                               "-",
                               "-",
                               cur->task_ring_count,
                               "-",
                               "-",
                               "-",
                               "-",
                               "-",
                               avg_task_proc,
                               avg_notify);
        } else {
        }
    }

    double total_rx_pps = 0, total_drop_pps = 0, total_off_tps = 0, total_comp_mps = 0, total_proc_tps = 0,
           total_upd_sps_timer = 0;
    if (dashboard_prev_stats_initialized) {
        total_rx_pps = (current_global_stats.total_rx_pkts >= dashboard_prev_stats.global.total_rx_pkts)
            ? (double) (current_global_stats.total_rx_pkts - dashboard_prev_stats.global.total_rx_pkts) /
                total_interval_sec_dashboard
            : 0;
        total_drop_pps = (current_global_stats.total_dropped_pkts >= dashboard_prev_stats.global.total_dropped_pkts)
            ? (double) (current_global_stats.total_dropped_pkts - dashboard_prev_stats.global.total_dropped_pkts) /
                total_interval_sec_dashboard
            : 0;
        total_off_tps =
            (current_global_stats.total_tasks_offloaded >= dashboard_prev_stats.global.total_tasks_offloaded)
            ? (double) (current_global_stats.total_tasks_offloaded -
                        dashboard_prev_stats.global.total_tasks_offloaded) /
                total_interval_sec_dashboard
            : 0;
        total_comp_mps =
            (current_global_stats.total_completion_msgs >= dashboard_prev_stats.global.total_completion_msgs)
            ? (double) (current_global_stats.total_completion_msgs -
                        dashboard_prev_stats.global.total_completion_msgs) /
                total_interval_sec_dashboard
            : 0;
        total_proc_tps =
            (current_global_stats.total_processed_tasks >= dashboard_prev_stats.global.total_processed_tasks)
            ? (double) (current_global_stats.total_processed_tasks -
                        dashboard_prev_stats.global.total_processed_tasks) /
                total_interval_sec_dashboard
            : 0;
        total_upd_sps_timer = (current_global_stats.total_updated_sessions_by_timer >=
                               dashboard_prev_stats.global.total_updated_sessions_by_timer)
            ? (double) (current_global_stats.total_updated_sessions_by_timer -
                        dashboard_prev_stats.global.total_updated_sessions_by_timer) /
                total_interval_sec_dashboard
            : 0;
    }

    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "|------|--------|---------|---------|------------|---------|---------|"
                       "-----------|----------|-----------|-----------|------------|-------------|------------|-------"
                       "-------|--------------|---------------|-------------|\n");
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "| Sum/ | Totals | %7.0f | %7.0f | %10.0f | %7.0f | %7.0f | %9.0f | %8" PRIu64 " | %9s | %9s | "
                       "%10s | %11s | "
                       "%10s | %12s | "
                       "%12s | %13s | "
                       "%11s |\n",
                       total_rx_pps,
                       total_drop_pps,
                       total_off_tps,
                       total_comp_mps,
                       total_proc_tps,
                       total_upd_sps_timer,
                       current_global_stats.total_sessions_count,
                       "-",
                       "-",
                       "-",   // Lookup
                       "-",   // Decap
                       "-",   // Bench
                       "-",   // Analyze
                       "-",   // Offload
                       "-",   // TaskProc
                       "-");  // Notify
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "-----------------------------------------------------------------------------------------------"
                       "-----------------------------------------------------------------------------------------------"
                       "--------------------------\n");

    dashboard_prev_stats.tsc = current_tsc_dashboard;
    memcpy(&dashboard_prev_stats.global, &current_global_stats, sizeof(dpif_global_stats_t));

    RTE_LCORE_FOREACH_WORKER(lcore_id) {
        if (rte_lcore_is_enabled(lcore_id)) {
            memcpy(&dashboard_prev_stats.core[lcore_id], &current_core_stats[lcore_id], sizeof(dpif_core_stats_t));
        }
    }

    dashboard_prev_stats_initialized = 1;

    pthread_mutex_unlock(&dashboard_stats_mutex);
}

/**
 * @brief Handles the 'help' CLI command.
 */
static void cli_cmd_help(int client_sock __rte_unused, int argc, char *argv[], char *response_buf) {
    if (argc == 1) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Available commands:\n");
        for (int i = 0; cli_command_table[i].name != NULL; ++i) {
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "  %-20s - %s\n",
                               cli_command_table[i].name,
                               cli_command_table[i].help_summary);
        }
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Type 'help <command_name>' for more details.\n");
    } else {
        for (int i = 0; cli_command_table[i].name != NULL; ++i) {
            if (strcmp(argv[1], cli_command_table[i].name) == 0) {
                append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "%s\n", cli_command_table[i].help_detail);
                return;
            }
        }
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Unknown command '%s' for help.\n", argv[1]);
    }
}

/**
 * @brief Handles the 'show_global_stats' CLI command.
 */
static void cli_cmd_show_global_stats(int client_sock __rte_unused,
                                      int argc __rte_unused,
                                      char *argv[] __rte_unused,
                                      char *response_buf) {
    if (!g_dpif_ctx) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: DPIF context not initialized.\n");
        return;
    }
    dpif_global_stats_t stats;
    if (dpif_get_global_stats(&stats) != 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Failed to get global stats.\n");
        return;
    }
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "DPIF Global Statistics (Totals):\n");
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Total RX Packets:                 %" PRIu64 "\n",
                       stats.total_rx_pkts);
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Total Dropped Packets:            %" PRIu64 "\n",
                       stats.total_dropped_pkts);
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Total Tasks Offloaded:            %" PRIu64 "\n",
                       stats.total_tasks_offloaded);
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Total Completion Msgs:    %" PRIu64 "\n",
                       stats.total_completion_msgs);
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Total Processed Tasks:    %" PRIu64 "\n",
                       stats.total_processed_tasks);
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Total Updated Sessions (by RX timer):%" PRIu64 "\n",
                       stats.total_updated_sessions_by_timer);  // Updated label
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Current Active Sessions:  %" PRIu64 "\n",
                       stats.total_sessions_count);
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Total Session Timeouts:   %" PRIu64 "\n",
                       stats.total_session_timeouts);
}

/**
 * @brief Formats and appends current core statistics to the response buffer.
 *
 * @param response_buf The response buffer.
 * @param s Pointer to the dpif_core_stats_t structure.
 */
static void format_core_stats_display(char *response_buf, const dpif_core_stats_t *s) {
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Lcore ID: %u, Type: %s\n", s->lcore_id, s->type);
    if (strcmp(s->type, "RX") == 0) {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    RX Pkts:      %" PRIu64 ", Dropped: %" PRIu64 "\n",
                           s->rx_pkts,
                           s->dropped_pkts);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Tasks Offload:%" PRIu64 ", Comp Msgs: %" PRIu64 "\n",
                           s->tasks_offloaded,
                           s->completion_msgs_processed);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Sessions:     %" PRIu64 " (cur), Timeouts: %" PRIu64 "\n",
                           s->sessions,
                           s->session_timeouts);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Updated by Timer: %" PRIu64 "\n",
                           s->updated_sessions_by_timer);
        append_to_response(
            response_buf,
            CLI_RESPONSE_BUFFER_SIZE,
            "    Avg Cycles (Lookup: %" PRIu64 ", Decap: %" PRIu64 ", Bench: %" PRIu64 ", Analyze: %" PRIu64
            ", Offload: %" PRIu64 ")\n",
            s->rx_pkts ? s->session_lookup_cycles / (s->rx_pkts + (s->rx_pkts == 0)) : 0,
            s->rx_pkts ? s->decap_cycles / (s->rx_pkts + (s->rx_pkts == 0)) : 0,
            s->rx_pkts ? s->benchmark_process_cycles / (s->rx_pkts + (s->rx_pkts == 0)) : 0,
            s->rx_pkts ? s->analyze_cycles / (s->rx_pkts + (s->rx_pkts == 0)) : 0,
            s->tasks_offloaded ? s->offload_cycles / (s->tasks_offloaded + (s->tasks_offloaded == 0)) : 0);
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Completion Ring Count: %u\n", s->completion_ring_count);
    } else if (strcmp(s->type, "Worker") == 0) {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Processed Tasks: %" PRIu64 "\n", s->processed_tasks);
        append_to_response(
            response_buf,
            CLI_RESPONSE_BUFFER_SIZE,
            "    Avg Cycles (TaskProc: %" PRIu64 ", Notify: %" PRIu64 ")\n",
            s->processed_tasks ? s->task_processing_cycles / (s->processed_tasks + (s->processed_tasks == 0)) : 0,
            s->processed_tasks ? s->notify_cycles / (s->processed_tasks + (s->processed_tasks == 0)) : 0);
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Task Ring Count: %u\n", s->task_ring_count);
    } else {  // Idle/Other
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "    (Idle or not an active DPIF data plane core)\n");
    }
}

/**
 * @brief Formats and appends total accumulated core statistics to the response buffer.
 *
 * @param response_buf The response buffer.
 * @param s Pointer to the dpif_core_stats_t structure.
 */
static void format_core_totals_display(char *response_buf, const dpif_core_stats_t *s) {
    append_to_response(
        response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Lcore ID: %u, Type: %s (Totals Since Start)\n", s->lcore_id, s->type);
    if (strcmp(s->type, "RX") == 0) {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "    RX Packets:               %" PRIu64 "\n", s->rx_pkts);
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Dropped Packets:            %" PRIu64 "\n", s->dropped_pkts);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Tasks Offloaded:            %" PRIu64 "\n",
                           s->tasks_offloaded);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Completion Msgs Rcvd:       %" PRIu64 "\n",
                           s->completion_msgs_processed);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Session Timeouts Handled:   %" PRIu64 "\n",
                           s->session_timeouts);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Sessions Updated by Timer:  %" PRIu64 "\n",
                           s->updated_sessions_by_timer);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Total Session Lookup Cycles: %" PRIu64 "\n",
                           s->session_lookup_cycles);
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Total Decap Cycles:          %" PRIu64 "\n", s->decap_cycles);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Total Benchmark Proc Cycles: %" PRIu64 "\n",
                           s->benchmark_process_cycles);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Total Analyze Cycles:        %" PRIu64 "\n",
                           s->analyze_cycles);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Total Offload Cycles:        %" PRIu64 "\n",
                           s->offload_cycles);
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Current Active Sessions:    %" PRIu64 "\n", s->sessions);
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Completion Ring (current):  %u\n", s->completion_ring_count);
    } else if (strcmp(s->type, "Worker") == 0) {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Processed Tasks:            %" PRIu64 "\n",
                           s->processed_tasks);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Total Task Processing Cycles: %" PRIu64 "\n",
                           s->task_processing_cycles);
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "    Total Notify Cycles:          %" PRIu64 "\n",
                           s->notify_cycles);
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Task Ring (current):        %u\n", s->task_ring_count);
    } else {  // Idle/Other
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "    (Lcore is Idle or not an active DPIF data plane core)\n");
    }
}

/**
 * @brief Handles the 'show_core_stats' CLI command.
 */
static void cli_cmd_show_core_stats(int client_sock __rte_unused, int argc, char *argv[], char *response_buf) {
    if (!g_dpif_ctx) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: DPIF context not initialized.\n");
        return;
    }
    dpif_core_stats_t core_stats;
    if (strcmp(argv[1], "all") == 0) {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "DPIF Per-Core Statistics (Current Snapshot/Averages):\n");
        uint32_t lcore_id;
        int found_any = 0;
        RTE_LCORE_FOREACH_WORKER(lcore_id) {
            if (dpif_get_core_stats(lcore_id, &core_stats) == 0) {
                if (strcmp(core_stats.type, "Idle") != 0 || core_stats.rx_pkts > 0 || core_stats.processed_tasks > 0) {
                    format_core_stats_display(response_buf, &core_stats);
                    found_any = 1;
                }
            }
        }
        if (!found_any) {
            append_to_response(
                response_buf, CLI_RESPONSE_BUFFER_SIZE, "  No active RX or Worker cores found with recent activity.\n");
        }
    } else {
        char *endptr;
        long lcore_id_long = strtol(argv[1], &endptr, 10);
        if (*endptr != '\0' || lcore_id_long < 0 || lcore_id_long >= RTE_MAX_LCORE) {
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "Error: Invalid lcore_id '%s'. Must be a number or 'all'.\n",
                               argv[1]);
            return;
        }
        uint32_t lcore_id = (uint32_t) lcore_id_long;
        if (dpif_get_core_stats(lcore_id, &core_stats) == 0) {
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "DPIF Core Statistics (Lcore %u - Current Snapshot/Averages):\n",
                               lcore_id);
            format_core_stats_display(response_buf, &core_stats);
        } else {
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "Error: Failed to get stats for lcore %u (or core is idle/not used by DPIF).\n",
                               lcore_id);
        }
    }
}

/**
 * @brief Handles the 'show_core_totals' CLI command.
 */
static void cli_cmd_show_core_totals(int client_sock __rte_unused, int argc, char *argv[], char *response_buf) {
    if (!g_dpif_ctx) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: DPIF context not initialized.\n");
        return;
    }
    dpif_core_stats_t core_stats;

    if (strcmp(argv[1], "all") == 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "DPIF Per-Core Accumulated Totals:\n");
        uint32_t lcore_id;
        int found_any_active = 0;
        RTE_LCORE_FOREACH_WORKER(lcore_id) {
            if (dpif_get_core_stats(lcore_id, &core_stats) == 0) {
                if (strcmp(core_stats.type, "Idle") != 0 || core_stats.rx_pkts > 0 || core_stats.processed_tasks > 0) {
                    format_core_totals_display(response_buf, &core_stats);
                    found_any_active = 1;
                }
            }
        }
        if (!found_any_active) {
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "  No active RX or Worker cores found with accumulated statistics.\n");
        }
    } else {
        char *endptr;
        long lcore_id_long = strtol(argv[1], &endptr, 10);
        if (*endptr != '\0' || lcore_id_long < 0 || lcore_id_long >= RTE_MAX_LCORE) {
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "Error: Invalid lcore_id '%s'. Must be a number or 'all'.\n",
                               argv[1]);
            return;
        }
        uint32_t lcore_id = (uint32_t) lcore_id_long;
        if (dpif_get_core_stats(lcore_id, &core_stats) == 0) {
            append_to_response(
                response_buf, CLI_RESPONSE_BUFFER_SIZE, "DPIF Core Accumulated Totals (Lcore %u):\n", lcore_id);
            format_core_totals_display(response_buf, &core_stats);
        } else {
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "Error: Failed to get stats for lcore %u (or core is idle/not used by DPIF).\n",
                               lcore_id);
        }
    }
}

/**
 * @brief Handles the 'list_sessions' (ls) CLI command.
 */
static void cli_cmd_list_sessions(int client_sock __rte_unused, int argc, char *argv[], char *response_buf) {
    if (!g_dpif_ctx) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: DPIF context not initialized.\n");
        return;
    }

    uint32_t max_sessions_to_display = DEFAULT_LS_COUNT;
    uint32_t user_requested_count = 0;

    if (argc > 1) {
        char *endptr;
        long count_long = strtol(argv[1], &endptr, 10);
        if (*endptr != '\0' || count_long <= 0) {
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "Error: Invalid max_count '%s'. Must be a positive number.\n",
                               argv[1]);
            return;
        }
        user_requested_count = (uint32_t) count_long;
        if (user_requested_count > MAX_SESSIONS_DISPLAY_AT_ONCE) {
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "Warning: Requested count %u exceeds maximum display "
                               "limit of %u. Truncating.\n",
                               user_requested_count,
                               MAX_SESSIONS_DISPLAY_AT_ONCE);
            max_sessions_to_display = MAX_SESSIONS_DISPLAY_AT_ONCE;
        } else {
            max_sessions_to_display = user_requested_count;
        }
        if (max_sessions_to_display == 0 && user_requested_count == 0) {
            max_sessions_to_display = 0;
        } else if (max_sessions_to_display == 0) {
            max_sessions_to_display = 1;
        }
    }

    uint32_t total_sessions_available = 0;
    dpif_get_all_session_info(NULL, 0, &total_sessions_available);
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Total active sessions: %u\n", total_sessions_available);

    if (total_sessions_available == 0 && max_sessions_to_display > 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "No active sessions to display.\n");
        return;
    }
    if (max_sessions_to_display > total_sessions_available) {
        max_sessions_to_display = total_sessions_available;
    }
    if (max_sessions_to_display == 0 && total_sessions_available > 0 &&
        argc == 1) {  // if "ls" and sessions exist, show default
        max_sessions_to_display =
            total_sessions_available > DEFAULT_LS_COUNT ? DEFAULT_LS_COUNT : total_sessions_available;
    }

    dpif_session_info_t *sessions_arr = NULL;
    if (max_sessions_to_display > 0) {
        sessions_arr =
            (dpif_session_info_t *) rte_calloc("CLI_SESS_ARR", max_sessions_to_display, sizeof(dpif_session_info_t), 0);
        if (!sessions_arr) {
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "Error: Failed to allocate memory for session array (size: %u).\n",
                               max_sessions_to_display);
            return;
        }
    }

    uint32_t filled_count = 0;
    if (max_sessions_to_display > 0) {
        if (dpif_get_all_session_info(sessions_arr, max_sessions_to_display, &filled_count) != 0) {
            append_to_response(
                response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Failed to retrieve session information.\n");
            if (sessions_arr)
                rte_free(sessions_arr);
            return;
        }
    }

    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "Displaying %u of %u sessions (use 'ls <count (max %u)>'):\n",
                       filled_count,
                       total_sessions_available,
                       MAX_SESSIONS_DISPLAY_AT_ONCE);

    if (filled_count > 0) {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "| %-10s | %-6s | %-39s : %-5s | %-39s : %-5s | %-3s | "
                           "%-4s | %-7s | %-9s | %-10s | %-9s | %-10s | %-10s |\n",
                           "AppSD",
                           "OwnLCO",
                           "SrcIP",
                           "SPrt",
                           "DstIP",
                           "Dprt",
                           "P",
                           "Task",
                           "Age(s)",
                           "PktsFwd",
                           "BytesFwd",
                           "PktsRev",
                           "BytesRev",
                           "Q(cnt:ovfl)");
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "|------------|--------|--------------------------------"
                           "---------:-------|-------------------------------------"
                           "----:-------|-----|------|---------|-----------|-------"
                           "-----|-----------|------------|------------|\n");

        uint64_t hz = rte_get_timer_hz();
        uint64_t current_tsc = rte_rdtsc();

        for (uint32_t i = 0; i < filled_count; ++i) {
            dpif_session_info_t *s = &sessions_arr[i];
            double age_sec = (hz > 0 && current_tsc > s->last_active_time_tsc)
                ? (double) (current_tsc - s->last_active_time_tsc) / hz
                : 0.0;
            char q_status[16];
            snprintf(q_status, sizeof(q_status), "%u:%c", s->inline_pkt_q_count, s->has_overflow_pkts ? 'Y' : 'N');

            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "| %-10d | %-6u | %-39s : %-5u | %-39s : %-5u | %-3u | %-4s | %-7.1f "
                               "| %-9" PRIu64 " | %-10" PRIu64 " | %-9" PRIu64 " | %-10" PRIu64 " | %-10s |\n",
                               s->app_session_descriptor,
                               s->owner_rx_lcore,
                               s->src_ip_str,
                               s->src_port_real,
                               s->dst_ip_str,
                               s->dst_port_real,
                               s->proto,
                               s->is_task_running ? "Run" : "Idle",
                               age_sec,
                               s->packets_fwd,
                               s->bytes_fwd,
                               s->packets_rev,
                               s->bytes_rev,
                               q_status);
        }
    }
    if (sessions_arr) {
        rte_free(sessions_arr);
    }
}

/**
 * @brief Handles the 'set_mode' CLI command to change DPIF operating mode.
 */
static void cli_cmd_set_mode(int client_sock __rte_unused, int argc, char *argv[], char *response_buf) {
    if (!g_dpif_ctx) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: DPIF context not initialized.\n");
        return;
    }

    if (strcmp(argv[1], "default") == 0) {
        g_dpif_ctx->benchmark_mode = DPIF_BENCHMARK_MODE_DEFAULT;
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "OK: DPIF mode set to DEFAULT.\n");
        DPIF_CLI_LOG_DEBUG("Operating mode changed to DEFAULT by CLI.");
    } else if (strcmp(argv[1], "echo") == 0) {
        g_dpif_ctx->benchmark_mode = DPIF_BENCHMARK_MODE_ECHO;
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "OK: DPIF mode set to ECHO.\n");
        DPIF_CLI_LOG_DEBUG("Operating mode changed to ECHO by CLI.");
    } else {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "Error: Unknown mode '%s'. Use 'normal' or 'bypass_verdict'.\n",
                           argv[1]);
    }
}

/**
 * @brief Helper to convert benchmark mode enum to string.
 */
static const char *benchmark_mode_to_str(dpif_benchmark_mode_t mode) {
    switch (mode) {
    case DPIF_BENCHMARK_MODE_DEFAULT:
        return "DEFAULT";
    case DPIF_BENCHMARK_MODE_ECHO:
        return "ECHO";
    case DPIF_BENCHMARK_MODE_DELAY:
        return "DELAY";
    case DPIF_BENCHMARK_MODE_RANDOM:
        return "RANDOM";
    default:
        return "UNKNOWN";
    }
}

/**
 * @brief Handles the 'show_config' (scfg) CLI command.
 */
static void cli_cmd_show_config(int client_sock __rte_unused,
                                int argc __rte_unused,
                                char *argv[] __rte_unused,
                                char *response_buf) {
    if (!g_dpif_ctx) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: DPIF context not initialized.\n");
        return;
    }

    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "--- DPIF System Configuration ---\n");

    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "\n[General Settings]\n");
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  App Name Prefix: %s\n",
                       g_dpif_ctx->app_cfg.app_name_prefix ? g_dpif_ctx->app_cfg.app_name_prefix : "N/A");
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Num RX Cores: %u\n", g_dpif_ctx->num_rx_cores);
    append_to_response(
        response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Num Worker Cores: %u\n", g_dpif_ctx->num_worker_cores);
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Session Timeout: %u seconds (%" PRIu64 " ticks)\n",
                       g_dpif_ctx->app_cfg.session_timeout_seconds,
                       g_dpif_ctx->session_timeout_ticks);
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Periodic Session Update Interval: %" PRIu64 " seconds (%" PRIu64 " ticks)\n",
                       g_dpif_ctx->app_cfg.session_update_interval_seconds,
                       g_dpif_ctx->periodic_session_update_ticks);
    append_to_response(response_buf,
                       CLI_RESPONSE_BUFFER_SIZE,
                       "  Benchmark Mode: %s\n",
                       benchmark_mode_to_str(g_dpif_ctx->benchmark_mode));
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Memif Port ID: %u\n", g_dpif_ctx->memif_port_id);

    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "\n[Lcore Assignments]\n");
    for (uint32_t i = 0; i < g_dpif_ctx->num_rx_cores; ++i) {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "  RX Core Index %u: Lcore %u\n", i, g_dpif_ctx->rx_lcore_map[i]);
    }
    for (uint32_t i = 0; i < g_dpif_ctx->num_worker_cores; ++i) {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "  Worker Core Index %u: Lcore %u\n",
                           i,
                           g_dpif_ctx->worker_lcore_map[i]);
    }

    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "\n[Memory Pools]\n");
    struct rte_mempool *pools[] = {g_dpif_ctx->mbuf_pool, g_dpif_ctx->session_pool, g_dpif_ctx->work_pool};
    const char *pool_types[] = {"MBUF", "Session", "Work"};
    for (int i = 0; i < 3; ++i) {
        if (pools[i]) {
            append_to_response(
                response_buf, CLI_RESPONSE_BUFFER_SIZE, "  %s Pool ('%s'):\n", pool_types[i], pools[i]->name);
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "    Capacity: %u, Element Size: %u, Cache Size: %u\n",
                               pools[i]->size,
                               pools[i]->elt_size,
                               pools[i]->cache_size);
            append_to_response(response_buf,
                               CLI_RESPONSE_BUFFER_SIZE,
                               "    Available: %u, In-Use: %u\n",
                               rte_mempool_avail_count(pools[i]),
                               rte_mempool_in_use_count(pools[i]));
        } else if (i == 2 && g_dpif_ctx->num_worker_cores == 0) {
            // Work pool is expected to be NULL if no workers
        } else {
            append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "  %s Pool: Not Initialized\n", pool_types[i]);
        }
    }

    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "\n[Rings]\n");
    if (g_dpif_ctx->all_task_rings && g_dpif_ctx->num_total_task_rings > 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Task Rings (RX -> Worker):\n");
        for (uint32_t i = 0; i < g_dpif_ctx->num_total_task_rings; ++i) {
            if (g_dpif_ctx->all_task_rings[i]) {
                struct rte_ring *r = g_dpif_ctx->all_task_rings[i];
                append_to_response(
                    response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Ring '%s' (for Worker Index %u):\n", r->name, i);
                append_to_response(response_buf,
                                   CLI_RESPONSE_BUFFER_SIZE,
                                   "      Capacity: %u, Count: %u, Free: %u\n",
                                   rte_ring_get_capacity(r),
                                   rte_ring_count(r),
                                   rte_ring_free_count(r));
            }
        }
    } else {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Task Rings: None (or no workers)\n");
    }

    if (g_dpif_ctx->all_completion_rings && g_dpif_ctx->num_total_completion_rings > 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Completion Rings (Worker -> RX):\n");
        for (uint32_t i = 0; i < g_dpif_ctx->num_total_completion_rings; ++i) {
            if (g_dpif_ctx->all_completion_rings[i]) {
                struct rte_ring *r = g_dpif_ctx->all_completion_rings[i];
                // Find which RX lcore this completion ring belongs to
                uint32_t owner_rx_lcore = RTE_MAX_LCORE;
                for (uint32_t rx_idx = 0; rx_idx < g_dpif_ctx->num_rx_cores; ++rx_idx) {
                    uint32_t lcore = g_dpif_ctx->rx_lcore_map[rx_idx];
                    if (lcore < RTE_MAX_LCORE && g_dpif_ctx->rx_contexts[lcore].completion_ring == r) {
                        owner_rx_lcore = lcore;
                        break;
                    }
                }
                if (owner_rx_lcore != RTE_MAX_LCORE) {
                    append_to_response(response_buf,
                                       CLI_RESPONSE_BUFFER_SIZE,
                                       "    Ring '%s' (for RX Lcore %u):\n",
                                       r->name,
                                       owner_rx_lcore);
                } else {
                    append_to_response(
                        response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Ring '%s' (for RX Index %u):\n", r->name, i);
                }
                append_to_response(response_buf,
                                   CLI_RESPONSE_BUFFER_SIZE,
                                   "      Capacity: %u, Count: %u, Free: %u\n",
                                   rte_ring_get_capacity(r),
                                   rte_ring_count(r),
                                   rte_ring_free_count(r));
            }
        }
    } else {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Completion Rings: None (or no workers/RX cores)\n");
    }

    if (g_dpif_ctx->rx_contexts) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Per-RX Core Free Session Index Rings:\n");
        for (uint32_t i = 0; i < g_dpif_ctx->num_rx_cores; ++i) {
            uint32_t lcore_id = g_dpif_ctx->rx_lcore_map[i];
            if (lcore_id < RTE_MAX_LCORE && g_dpif_ctx->rx_contexts[lcore_id].lcore_id == lcore_id) {
                dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[lcore_id];
                if (rx_ctx->free_session_indices_ring) {
                    struct rte_ring *r = rx_ctx->free_session_indices_ring;
                    append_to_response(
                        response_buf, CLI_RESPONSE_BUFFER_SIZE, "    Ring '%s' (for RX Lcore %u):\n", r->name, lcore_id);
                    append_to_response(response_buf,
                                       CLI_RESPONSE_BUFFER_SIZE,
                                       "      Capacity: %u, Count: %u, Free: %u\n",
                                       rte_ring_get_capacity(r),
                                       rte_ring_count(r),
                                       rte_ring_free_count(r));
                }
            }
        }
    }

    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "\n[Per-RX Core Session Hash Tables]\n");
    if (g_dpif_ctx->rx_contexts) {
        for (uint32_t i = 0; i < g_dpif_ctx->num_rx_cores; ++i) {
            uint32_t lcore_id = g_dpif_ctx->rx_lcore_map[i];
            if (lcore_id < RTE_MAX_LCORE && g_dpif_ctx->rx_contexts[lcore_id].lcore_id == lcore_id) {
                dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[lcore_id];
                if (rx_ctx->session_table) {
                    struct rte_hash *h = rx_ctx->session_table;
                    append_to_response(
                        response_buf, CLI_RESPONSE_BUFFER_SIZE, "  Hash Table (for RX Lcore %u):\n", lcore_id);
                    append_to_response(response_buf,
                                       CLI_RESPONSE_BUFFER_SIZE,
                                       "    Max Entries (Capacity): %u, Current Count: %d\n",
                                       g_dpif_spec_settings.session_hash_entries,
                                       rte_hash_count(h));
                }
            }
        }
    }
    const char *runtime_version = rte_version();
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "\nDPDK Version: %s\n", runtime_version);
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "--- End of Configuration ---\n");
}

/**
 * @brief Handles the 'set_verdict' CLI command.
 *
 * Manually sends a verdict for a specific session ID.
 */
static void cli_cmd_set_verdict(int client_sock __rte_unused, int argc, char *argv[], char *response_buf) {
    if (!g_dpif_ctx) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: DPIF context not initialized.\n");
        return;
    }

    // 1. Parse session_id
    char *endptr;
    long session_id_long = strtol(argv[1], &endptr, 10);
    if (*endptr != '\0' || session_id_long < 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid session_id '%s'.\n", argv[1]);
        return;
    }
    int session_id = (int) session_id_long;

    // 2. Parse verdict string
    uint32_t verdict_val;
    if (strcasecmp(argv[2], "accept") == 0) {
        verdict_val = 1;
    } else if (strcasecmp(argv[2], "drop") == 0) {
        verdict_val = 0;
    } else {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Invalid verdict '%s'. Use 'accept' or 'drop'.\n", argv[2]);
        return;
    }

    // 3. Call the existing libdpif function to set the verdict
    int ret = dpif_set_session_verdict(session_id, verdict_val);

    // 4. Report result back to the CLI client
    if (ret == 0) {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "OK: Verdict '%s' (%u) sent for session %d.\n",
                           argv[2],
                           verdict_val,
                           session_id);
    } else if (ret == -ENOENT) {
        append_to_response(
            response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Session %d not found or invalid.\n", session_id);
    } else {
        append_to_response(response_buf,
                           CLI_RESPONSE_BUFFER_SIZE,
                           "Error: Failed to set verdict for session %d (code: %d).\n",
                           session_id,
                           ret);
    }
}

/**
 * @brief Handles the 'force-all-pending' CLI command.
 *
 * Forces all active sessions to have PENDING verdict for DPI performance testing.
 * This allows all packets to be sent to DPIU for processing.
 */
static void cli_cmd_force_all_pending(int client_sock __rte_unused, int argc, char *argv[], char *response_buf) {
    if (!g_dpif_ctx) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: DPIF context not initialized.\n");
        return;
    }

    // Get total number of active sessions
    uint32_t total_sessions = 0;
    if (dpif_get_all_session_info(NULL, 0, &total_sessions) != 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Failed to get session count.\n");
        return;
    }

    if (total_sessions == 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "No active sessions found.\n");
        return;
    }

    // Allocate memory for session info array
    dpif_session_info_t *sessions_arr = (dpif_session_info_t *)rte_calloc("CLI_FORCE_PENDING",
                                                                          total_sessions,
                                                                          sizeof(dpif_session_info_t), 0);
    if (!sessions_arr) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE,
                          "Error: Failed to allocate memory for %u sessions.\n", total_sessions);
        return;
    }

    // Get all session information
    uint32_t filled_count = 0;
    if (dpif_get_all_session_info(sessions_arr, total_sessions, &filled_count) != 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE, "Error: Failed to retrieve session information.\n");
        rte_free(sessions_arr);
        return;
    }

    // Force all sessions to PENDING verdict (value 2 for DPI_VERDICT_PENDING)
    uint32_t success_count = 0;
    uint32_t error_count = 0;

    for (uint32_t i = 0; i < filled_count; i++) {
        int ret = dpif_set_session_verdict(sessions_arr[i].app_session_descriptor, 2); // DPI_VERDICT_PENDING = 2
        if (ret == 0) {
            success_count++;
        } else {
            error_count++;
        }
    }

    // Report results
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE,
                      "Force PENDING verdict completed:\n");
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE,
                      "  Total sessions processed: %u\n", filled_count);
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE,
                      "  Successfully set to PENDING: %u\n", success_count);
    if (error_count > 0) {
        append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE,
                          "  Errors encountered: %u\n", error_count);
    }
    append_to_response(response_buf, CLI_RESPONSE_BUFFER_SIZE,
                      "\nAll packets will now be sent to DPIU for DPI processing.\n");

    rte_free(sessions_arr);
}

/**
 * @brief Handles a single client connection.
 * Reads commands, processes them, and sends responses.
 *
 * @param client_sock The socket descriptor for the connected client.
 */
static void handle_client_connection(int client_sock) {
    char cmd_buffer[CLI_CMD_BUFFER_SIZE];
    static char response_buffer_static[CLI_RESPONSE_BUFFER_SIZE];

    ssize_t n_read;
    char *argv[MAX_CLI_ARGS];
    int argc;

    while ((n_read = read(client_sock, cmd_buffer, sizeof(cmd_buffer) - 1)) > 0) {
        cmd_buffer[n_read] = '\0';
        cmd_buffer[strcspn(cmd_buffer, "\r\n")] = 0;

        char *start = cmd_buffer;
        while (isspace((unsigned char) *start))
            start++;
        char *end = start + strlen(start) - 1;
        while (end > start && isspace((unsigned char) *end))
            end--;
        *(end + 1) = '\0';

        DPIF_CLI_LOG_DEBUG("Received command: '%s'", start);
        response_buffer_static[0] = '\0';

        if (strlen(start) == 0) {
            // No response for empty command
        } else if (strcmp(start, "quit") == 0 || strcmp(start, "exit") == 0) {
            break;
        } else {
            argc = 0;
            char *token;
            char *saveptr;
            for (token = strtok_r(start, " ", &saveptr); token != NULL && argc < MAX_CLI_ARGS;
                 token = strtok_r(NULL, " ", &saveptr)) {
                argv[argc++] = token;
            }

            if (argc > 0) {
                int cmd_found = 0;
                for (int i = 0; cli_command_table[i].name != NULL; ++i) {
                    if (strcmp(argv[0], cli_command_table[i].name) == 0) {
                        if (argc < cli_command_table[i].min_args) {
                            append_to_response(response_buffer_static,
                                               CLI_RESPONSE_BUFFER_SIZE,
                                               "Error: Insufficient arguments.\n%s\n",
                                               cli_command_table[i].help_detail);
                        } else {
                            cli_command_table[i].handler(client_sock, argc, argv, response_buffer_static);
                        }
                        cmd_found = 1;
                        break;
                    }
                }
                if (!cmd_found) {
                    append_to_response(response_buffer_static,
                                       CLI_RESPONSE_BUFFER_SIZE,
                                       "Error: Unknown command '%s'. Type 'help'.\n",
                                       argv[0]);
                }
            }
        }

        if (strlen(response_buffer_static) > 0) {
            if (write(client_sock, response_buffer_static, strlen(response_buffer_static)) < 0) {
                DPIF_CLI_LOG_ERROR("Failed to send response: %s", strerror(errno));
                break;
            }
        }
    }

    if (n_read < 0) {
        DPIF_CLI_LOG_ERROR("Error reading from client: %s", strerror(errno));
    }
}

/**
 * @brief Main function for the CLI server thread.
 * Listens for client connections and handles them.
 *
 * @param arg Thread argument (unused).
 * @return NULL.
 */
static void *cli_server_thread_main(void *arg __rte_unused) {
    int server_sock, client_sock;
    struct sockaddr_un server_addr, client_addr;
    socklen_t client_addr_len = sizeof(client_addr);

    server_sock = socket(AF_UNIX, SOCK_STREAM, 0);
    if (server_sock < 0) {
        DPIF_CLI_LOG_ERROR("Failed to create socket: %s", strerror(errno));
        return NULL;
    }
    unlink(CLI_SOCKET_PATH);
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sun_family = AF_UNIX;
    strncpy(server_addr.sun_path, CLI_SOCKET_PATH, sizeof(server_addr.sun_path) - 1);

    if (bind(server_sock, (struct sockaddr *) &server_addr, sizeof(server_addr)) < 0) {
        DPIF_CLI_LOG_ERROR("Failed to bind socket '%s': %s", CLI_SOCKET_PATH, strerror(errno));
        close(server_sock);
        return NULL;
    }
    if (listen(server_sock, 5) < 0) {
        DPIF_CLI_LOG_ERROR("Failed to listen on socket: %s", strerror(errno));
        close(server_sock);
        return NULL;
    }
    DPIF_CLI_LOG_DEBUG("CLI server listening on %s", CLI_SOCKET_PATH);

    while (!cli_server_quit_flag) {
        fd_set readfds;
        struct timeval tv;
        int activity;

        FD_ZERO(&readfds);
        FD_SET(server_sock, &readfds);
        tv.tv_sec = 1;
        tv.tv_usec = 0;

        activity = select(server_sock + 1, &readfds, NULL, NULL, &tv);

        if ((activity < 0) && (errno != EINTR)) {
            DPIF_CLI_LOG_WARNING("select() error: %s", strerror(errno));
        } else if (activity > 0 && FD_ISSET(server_sock, &readfds)) {
            client_sock = accept(server_sock, (struct sockaddr *) &client_addr, &client_addr_len);
            if (client_sock < 0) {
                DPIF_CLI_LOG_ERROR("Failed to accept connection: %s", strerror(errno));
                continue;
            }
            DPIF_CLI_LOG_DEBUG("CLI client connected.");
            handle_client_connection(client_sock);
            close(client_sock);
            DPIF_CLI_LOG_DEBUG("CLI client disconnected.");
        }
    }

    DPIF_CLI_LOG_DEBUG("CLI server shutting down.");
    close(server_sock);
    unlink(CLI_SOCKET_PATH);
    return NULL;
}

/// @see dpif_cli_server.h
int dpif_cli_server_start(void) {
    cli_server_quit_flag = 0;
    if (pthread_create(&cli_server_tid, NULL, cli_server_thread_main, NULL) != 0) {
        DPIF_CLI_LOG_ERROR("Failed to create CLI server thread: %s", strerror(errno));
        return -errno;
    }
    DPIF_CLI_LOG_DEBUG("CLI server thread creation requested.");
    return 0;
}

/// @see dpif_cli_server.h
void dpif_cli_server_stop(void) {
    if (cli_server_tid == (pthread_t) 0) {
        return;
    }
    DPIF_CLI_LOG_DEBUG("Signaling CLI server to stop...");
    cli_server_quit_flag = 1;

    pthread_join(cli_server_tid, NULL);
    DPIF_CLI_LOG_DEBUG("CLI server thread stopped.");
    cli_server_tid = (pthread_t) 0;
}

int dpif_execute_cli_command(const char *command, char *response_buf, size_t buf_size) {
    int sock_fd;
    struct sockaddr_un server_addr;
    ssize_t n_written, n_read;
    size_t total_read = 0;

    if (!command || !response_buf || buf_size == 0) {
        return -1;
    }

    response_buf[0] = '\0';

    sock_fd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (sock_fd < 0) {
        return -1;
    }

    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sun_family = AF_UNIX;
    strncpy(server_addr.sun_path, CLI_SOCKET_PATH, sizeof(server_addr.sun_path) - 1);

    if (connect(sock_fd, (struct sockaddr *) &server_addr, sizeof(server_addr)) < 0) {
        close(sock_fd);
        return -1;
    }

    char cmd_with_newline[CLI_CMD_BUFFER_SIZE];
    snprintf(cmd_with_newline, sizeof(cmd_with_newline), "%s\n", command);

    n_written = write(sock_fd, cmd_with_newline, strlen(cmd_with_newline));
    if (n_written < 0) {
        close(sock_fd);
        return -1;
    }

    const char *quit_cmd = "quit\n";
    write(sock_fd, quit_cmd, strlen(quit_cmd));

    struct pollfd pfd;
    pfd.fd = sock_fd;
    pfd.events = POLLIN;

    while (total_read < buf_size - 1) {
        int ret = poll(&pfd, 1, 2000);
        if (ret < 0) {
            break;
        }
        if (ret == 0) {
            break;
        }

        if (pfd.revents & POLLIN) {
            n_read = read(sock_fd, response_buf + total_read, buf_size - total_read - 1);
            if (n_read > 0) {
                total_read += n_read;
            } else {
                break;
            }
        }
    }

    response_buf[total_read] = '\0';

    close(sock_fd);
    return 0;
}