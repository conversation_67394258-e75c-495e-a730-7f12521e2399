# NSA Thread Configuration - Compilation Test

## Overview
This document outlines the compilation test for the new NSA thread configuration functionality.

## Modified Files

### 1. nsa/src/nsa.h
- Added `num_rx_threads` and `num_worker_threads` fields to `nsa_root_t` structure
- These fields store the command line thread configuration

### 2. nsa/src/nsa_main.c
- Updated `nsa_usage()` function to show new `-x` and `-w` parameters
- Modified command line parsing to handle `-x` (RX threads) and `-w` (Worker threads)
- Updated DPIF configuration to use command line parameters or platform defaults
- Added logging for thread configuration

### 3. libdpif/lib/dpif_init.c
- Replaced hardcoded EAL initialization with dynamic EAL argument generation
- Added `build_dynamic_eal_args()` function to create EAL args based on thread count
- Added `update_memif_configuration()` function to set memif queues = RX threads
- Updated `initialize_eal()` to use dynamic arguments
- Enhanced logging for better debugging

### 4. libdpif/lib/dpif_cli_server.c
- Added `cli_cmd_force_all_pending()` function
- Added "force-all-pending" command to CLI command table
- Function iterates through all active sessions and sets verdict to PENDING

### 5. nsa/src/nsa_cli.c
- Added `nsa_cli_debug_force_pending()` function
- Created `nsa_cli_debug_commands[]` array for debug commands
- Added "debug" category to root CLI command tree
- Added "force-pending-all" command under debug category

## Key Architectural Improvements

### 1. Dynamic EAL Initialization
- **Before**: Hardcoded lcore ranges (e.g., "13-23", "2-5")
- **After**: Dynamic calculation based on thread requirements
- **Formula**: Total lcores = 1 (main) + RX threads + Worker threads

### 2. Adaptive memif Configuration
- **Before**: Fixed queue count (usually 2)
- **After**: Queue count = RX thread count
- **Benefit**: Proper load balancing across RX threads

### 3. Intelligent Resource Allocation
- **Before**: Fixed resource allocation regardless of actual needs
- **After**: Resources allocated based on actual thread configuration
- **Benefit**: Better resource utilization and scalability

## Compilation Test Steps

### 1. Basic Compilation Test
```bash
# Test NSA compilation
cd nsa
make clean
make

# Test libdpif compilation  
cd ../libdpif
make clean
make
```

### 2. Syntax Verification
- All modified files should compile without warnings
- No undefined symbols or missing declarations
- Proper header inclusions maintained

### 3. Functional Test Points
- NSA should start with default thread configuration
- NSA should accept `-x` and `-w` parameters
- EAL should initialize with correct lcore count
- memif should configure correct queue count
- CLI debug command should be accessible

## Expected Behavior

### 1. Default Behavior (no parameters)
```bash
./nsad -f
# Should use platform-specific defaults:
# AEGIS: 8 RX, 2 Worker
# SMBSIM: 2 RX, 1 Worker
```

### 2. Custom RX Threads
```bash
./nsad -x 16 -f
# Should use: 16 RX, default Worker threads
# EAL: 1 + 16 + default_worker lcores
# memif: 16 queues
```

### 3. Custom Worker Threads
```bash
./nsad -w 8 -f  
# Should use: default RX, 8 Worker threads
# EAL: 1 + default_rx + 8 lcores
```

### 4. Both Custom
```bash
./nsad -x 12 -w 6 -f
# Should use: 12 RX, 6 Worker threads
# EAL: 1 + 12 + 6 = 19 lcores
# memif: 12 queues
```

### 5. Debug Command
```bash
dcli nsad debug force-pending-all
# Should force all active sessions to PENDING verdict
# Should report processing results
```

## Validation Checklist

- [ ] NSA compiles without errors
- [ ] libdpif compiles without errors  
- [ ] No new compiler warnings introduced
- [ ] Help message shows new parameters
- [ ] Command line parsing works correctly
- [ ] EAL initialization uses dynamic arguments
- [ ] memif configuration adapts to RX thread count
- [ ] Debug CLI command is accessible
- [ ] Error handling works for invalid parameters
- [ ] Logging provides sufficient debugging information

## Performance Testing Workflow

1. **Baseline Test**: Start with default configuration
2. **Scale RX Threads**: Test with 2, 4, 8, 16, 32 RX threads
3. **Force PENDING**: Use debug command to force all sessions to PENDING
4. **Measure DPI Performance**: Monitor packet processing rates
5. **Compare Results**: Analyze performance across different thread counts

This comprehensive test ensures the new functionality works correctly and provides the foundation for DPI performance testing.
